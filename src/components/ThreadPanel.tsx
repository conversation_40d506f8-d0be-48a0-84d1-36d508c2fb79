import React, { useRef, useEffect } from 'react';
import { useApp } from '@/lib/app-context';
import { Message } from './Message';
import { MessageInput } from './MessageInput';
import { X } from 'lucide-react';
// import { findUserById } from '@/lib/mock-data'; // Removed mock data import
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';

export const ThreadPanel = () => {
  const { workspace, setActiveThread, currentChannel, currentDirectMessage, loadMoreThreadReplies, getActiveThreadReplies, currentThreadParentMessage } = useApp();
  const { selectedMessageId, setSelectedMessageId } = useKeyboardShortcuts();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  // const inputRef = useRef<HTMLTextAreaElement>(null); // Removed as MessageInput handles its own focus via autoFocus
  const threadPanelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Scroll to bottom when thread changes
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    // Focusing is handled by MessageInput's autoFocus prop
    // and its internal useEffect for focusing the SimpleMDE editor.
  }, [workspace?.activeThreadId]); // Depend on activeThreadId

  const activeThreadReplies = getActiveThreadReplies();

  // Auto-scroll when messages are added
  useEffect(() => {
    if (activeThreadReplies?.messages) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [activeThreadReplies?.messages.length]);

  // Add ESC key handler to close thread panel
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setActiveThread(null);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [setActiveThread]);

  if (!workspace?.activeThreadId || !currentThreadParentMessage) {
    return null;
  }

  const parentMessage = currentThreadParentMessage;
  const parentUser = workspace?.users.find(u => u.id === parentMessage.userId);

  // activeThreadReplies contains the actual replies, excluding the parent.
  const replies = activeThreadReplies?.messages || [];
  const replyCount = replies.length;

  return (
    <div
      ref={threadPanelRef}
      className="w-full lg:w-80 xl:w-96 h-full flex flex-col lg:border-l border-[var(--app-border)] bg-[var(--app-main-bg)]"
      tabIndex={-1}
    >
      {/* Thread Header */}
      <div className="thread-header justify-between">
        <div className="flex items-center">
          <h3 className="font-medium text-[var(--app-main-text)]">Thread</h3>
          {parentUser && (
            <span className="text-xs ml-2 opacity-70">
              with {parentUser.name}
            </span>
          )}
        </div>
        <button
          onClick={() => setActiveThread(null)}
          className="p-1 rounded-md hover:bg-[var(--app-hover-bg)]"
          aria-label="Close thread"
          title="Close thread (Esc)"
          data-shortcut="Esc"
        >
          <X size={18} className="text-[var(--app-main-text)] opacity-70" />
        </button>
      </div>

      {/* Thread Messages */}
      <div className="flex-1 overflow-y-auto p-4 user-select-text">
        {/* Parent message shown in a more compact form */}
        <div className="mb-6 pb-3 border-b border-[var(--app-border)]">
          <Message message={parentMessage} isParentMessage={true} isInThread={true} />
        </div>

        {/* Load Newer Replies Button */}
        {activeThreadReplies?.isLoadingNewer && (
          <div className="text-center py-2 text-xs text-[var(--app-secondary-text)]">Loading newer replies...</div>
        )}
        {activeThreadReplies?.hasMoreNewer && !activeThreadReplies?.isLoadingNewer && (
          <div className="text-center py-2">
            <button
              onClick={() => loadMoreThreadReplies(parentMessage.id, currentChannel?.id || currentDirectMessage!.id, currentChannel ? 'channel' : 'dm', 'newer')}
              className="text-xs text-[var(--app-accent-text)] hover:underline"
            >
              Load Newer Replies
            </button>
          </div>
        )}

        {/* Thread replies counter - always visible and sticky */}
        <div className="text-xs font-medium mb-4 sticky top-0 bg-[var(--app-main-bg)] py-2 border-b border-[var(--app-border)] z-10 text-[var(--app-main-text)] opacity-70">
          {replyCount > 0
            ? `${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`
            : 'No replies yet'}
        </div>
        
        {/* Load Older Replies Button */}
        {activeThreadReplies?.isLoadingOlder && (
          <div className="text-center py-2 text-xs text-[var(--app-secondary-text)]">Loading older replies...</div>
        )}
        {activeThreadReplies?.hasMoreOlder && !activeThreadReplies?.isLoadingOlder && (
          <div className="text-center py-2">
            <button
              onClick={() => loadMoreThreadReplies(parentMessage.id, currentChannel?.id || currentDirectMessage!.id, currentChannel ? 'channel' : 'dm', 'older')}
              className="text-xs text-[var(--app-accent-text)] hover:underline"
            >
              Load Older Replies
            </button>
          </div>
        )}

        {replies.length > 0 ? (
          <div className="space-y-6">
            {replies.map(replyMessage => (
              <Message
                key={replyMessage.id}
                message={replyMessage}
                isInThread={true}
              />
            ))}
            <div ref={messagesEndRef} />
          </div>
        ) : (
          !activeThreadReplies?.isLoadingOlder && !activeThreadReplies?.isLoadingNewer && <div ref={messagesEndRef} />
        )}
      </div>

      {/* Thread Input */}
      <div className="p-3 border-t border-[var(--app-border)]">
        <MessageInput threadId={parentMessage.id} placeholder="Reply in thread..." autoFocus={true} />
      </div>
    </div>
  );
};
