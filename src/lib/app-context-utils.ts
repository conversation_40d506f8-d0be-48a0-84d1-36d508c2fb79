import { DirectMessage, Message, Section, Channel, Workspace, UserSettings, WorkspaceSettings, User, ThreadRepliesData } from './types';

/**
 * Removes duplicate DM conversations.
 * Keeps the first DM for each unique participant pairing,
 * preferring DMs with messages if multiple exist for the same pair.
 */
export const deduplicateDirectMessages = (dms: DirectMessage[], currentUserId: string): DirectMessage[] => {
  const userDmMap = new Map<string, DirectMessage>();

  for (const dm of dms) {
    if (dm.participants.length === 2) {
      const otherUserId = dm.participants.find(id => id !== currentUserId);
      if (otherUserId) {
        const existingDm = userDmMap.get(otherUserId);
        if (!existingDm || (dm.messages && dm.messages.length > 0 && (!existingDm.messages || existingDm.messages.length === 0))) {
          userDmMap.set(otherUserId, dm);
        }
      }
    } else {
      const key = dm.participants.sort().join('-');
      if (!userDmMap.has(key) || (dm.messages && dm.messages.length > 0 && (!userDmMap.get(key)?.messages?.length))) {
        userDmMap.set(key, dm);
      }
    }
  }
  return Array.from(userDmMap.values());
};

/**
 * Finds a section by its ID from a list of sections.
 */
export const findSectionById = (sections: Section[], sectionId: string | null): Section | undefined => {
  if (!sectionId) return undefined;
  return sections.find(section => section.id === sectionId);
};

/**
 * Finds a message by its ID from anywhere in the workspace (channels, DMs, top-level).
 */
const findMessageById = (workspace: Workspace, messageId: string): Message | undefined => {
  for (const section of workspace.sections) {
    for (const channel of section.channels) {
      const msg = channel.messages.find(m => m.id === messageId);
      if (msg) return msg;
      if (channel.threads) {
        for (const threadId in channel.threads) {
          const threadMsg = channel.threads[threadId].messages.find(m => m.id === messageId);
          if (threadMsg) return threadMsg;
        }
      }
    }
  }
  for (const dm of workspace.directMessages) {
    const msg = dm.messages.find(m => m.id === messageId);
    if (msg) return msg;
    if (dm.threads) {
      for (const threadId in dm.threads) {
        const threadMsg = dm.threads[threadId].messages.find(m => m.id === messageId);
        if (threadMsg) return threadMsg;
      }
    }
  }
  return undefined;
};

/**
 * Finds a channel by its ID from a list of sections.
 */
export const findChannelById = (sections: Section[], channelId: string | null): Channel | undefined => {
  if (!channelId) return undefined;
  for (const section of sections) {
    const channel = section.channels.find(ch => ch.id === channelId);
    if (channel) return channel;
  }
  return undefined;
};

/**
 * Transforms a raw message object from Supabase
 * into the client-side Message type.
 */
export const transformSupabaseMessage = (dbMessage: any): Message => {
  const message: Message = {
    id: dbMessage.id,
    content: dbMessage.content,
    timestamp: dbMessage.timestamp || dbMessage.created_at,
    userId: dbMessage.user_id,
    channelId: dbMessage.dm_id || dbMessage.channel_id,
    parent_message_id: dbMessage.parent_message_id,
    topicId: dbMessage.topic_id,
    reactions: dbMessage.reactions || [],
    reactions_summary: dbMessage.reactions_summary || [],
    editedTimestamp: dbMessage.edited_at || dbMessage.editedTimestamp, // check both due to potential inconsistencies
    files: dbMessage.files?.map((f: any) => ({
      id: f.id,
      name: f.name,
      type: f.type,
      url: f.url,
      size_bytes: f.size_bytes,
      uploaded_by_user_id: f.uploaded_by_user_id,
      created_at: f.created_at,
      message_id: f.message_id,
      channel_id: f.channel_id,
      is_pinned_in_channel_id: f.is_pinned_in_channel_id,
    })) || [],
    alsoSendToChannel: dbMessage.also_send_to_channel,
    reply_count: dbMessage.reply_count,
  };
  return message;
};

/**
 * Organizes messages into regular (top-level) messages and a simplified threads structure.
 * This function is primarily for initial data processing if a mixed list is received.
 * With on-demand thread loading, its direct use might be limited.
 */
export const organizeMessagesIntoThreads = (messages: Message[]): {
  regularMessages: Message[],
  threads: Record<string, { messages: Message[] }>
} => {
  const regularMessages: Message[] = [];
  const threadsOutput: Record<string, { messages: Message[] }> = {};

  messages.forEach((message) => {
    if (message.parent_message_id) {
      const parentId = message.parent_message_id;
      if (!threadsOutput[parentId]) {
        threadsOutput[parentId] = { messages: [] };
      }
      if (!threadsOutput[parentId].messages.some(m => m.id === message.id)) {
         threadsOutput[parentId].messages.push(message);
      }
    } else {
      regularMessages.push(message);
      if (threadsOutput[message.id]) { // If this top-level message is a parent
        if (!threadsOutput[message.id].messages.some(m => m.id === message.id)) {
          threadsOutput[message.id].messages.unshift(message); // Ensure parent is first
        }
      }
    }
  });

  for (const threadId in threadsOutput) {
    threadsOutput[threadId].messages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  }
  regularMessages.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

  return { regularMessages, threads: threadsOutput };
};


/**
 * Finds an existing thread's messages or creates a new simplified thread structure from a parent message.
 * NOTE: This function's utility is reduced with the new ThreadRepliesData structure.
 */
export const findOrCreateThreadFromContext = (
  messageId: string, // This is the parent_message_id
  channelOrDmContext: Channel | DirectMessage | null
): { messages: Message[] } | null => {
  if (!channelOrDmContext) return null;

  if (channelOrDmContext.threads && channelOrDmContext.threads[messageId]) {
    return { messages: channelOrDmContext.threads[messageId].messages };
  }

  const parentMessage = channelOrDmContext.messages.find(m => m.id === messageId);
  if (parentMessage) {
    return { messages: [parentMessage] };
  }
  return null;
};

/**
 * Updates an optimistically added message in an array with its final version from the database.
 */
export const updateOptimisticMessageInArray = (
  messagesArray: Message[],
  clientTempId: string,
  finalMessage: Message
): Message[] => {
  const msgIndex = messagesArray.findIndex(m => m.id === clientTempId);
  let updatedMessages = [...messagesArray];

  if (msgIndex !== -1) {
    const optimisticEntry = updatedMessages[msgIndex];
    updatedMessages[msgIndex] = {
      ...optimisticEntry,
      ...finalMessage,
      files: (finalMessage.files && finalMessage.files.length > 0) ? finalMessage.files : optimisticEntry.files
    };
  }
  return updatedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
};

/**
 * Merges newly loaded workspace data with a previous workspace state.
 */
export const mergeWorkspaceData = (
  prevWorkspace: Workspace | null,
  loadedWorkspace: Workspace
): Workspace => {
  if (!prevWorkspace) return loadedWorkspace;

  const newMergedWorkspace: Workspace = {
    ...prevWorkspace,
    ...loadedWorkspace,
  };

  newMergedWorkspace.sections = loadedWorkspace.sections.map(newSection => {
    const prevSection = prevWorkspace.sections.find(ps => ps.id === newSection.id);
    return {
      ...newSection,
      channels: newSection.channels.map(newChannel => {
        const prevChannel = prevSection?.channels.find(pc => pc.id === newChannel.id);
        if (prevChannel) {
          return {
            ...newChannel,
            messages: prevChannel.messages || [],
            threads: prevChannel.threads || {}, // Preserve loaded threads
            channelTopics: prevChannel.channelTopics || [],
            files: prevChannel.files || [],
            unreadCount: prevChannel.unreadCount !== undefined ? prevChannel.unreadCount : newChannel.unreadCount,
          };
        }
        return newChannel;
      }),
    };
  });

  if (loadedWorkspace.directMessages && loadedWorkspace.directMessages.length > 0) {
    newMergedWorkspace.directMessages = loadedWorkspace.directMessages.map(newDM => {
      const prevDM = prevWorkspace.directMessages.find(pdm => pdm.id === newDM.id);
      if (prevDM) {
        return {
          ...newDM,
          messages: prevDM.messages || [],
          threads: prevDM.threads || {}, // Preserve loaded threads
          topics: prevDM.topics || [],
          unreadCount: prevDM.unreadCount !== undefined ? prevDM.unreadCount : newDM.unreadCount,
        };
      }
      return newDM;
    });
  } else if (prevWorkspace.directMessages && prevWorkspace.directMessages.length > 0 && (!loadedWorkspace.directMessages || loadedWorkspace.directMessages.length === 0)) {
    newMergedWorkspace.directMessages = prevWorkspace.directMessages;
  } else {
    newMergedWorkspace.directMessages = loadedWorkspace.directMessages || [];
  }

  newMergedWorkspace.currentChannelId = loadedWorkspace.currentChannelId;
  newMergedWorkspace.currentSectionId = loadedWorkspace.currentSectionId;
  newMergedWorkspace.currentDirectMessageId = loadedWorkspace.currentDirectMessageId;
  newMergedWorkspace.activeThreadId = loadedWorkspace.activeThreadId;
  newMergedWorkspace.users = loadedWorkspace.users;
  newMergedWorkspace.settings = loadedWorkspace.settings;

  return newMergedWorkspace;
};

/**
 * Applies a new realtime message (INSERT or DELETE) to the workspace state.
 */
export const applyRealtimeMessageToWorkspace = (
  prevWs: Workspace,
  payload: { new?: any; old?: any; eventType: 'INSERT' | 'DELETE' }, // Modified to include eventType and old payload
  currentAuthUserId: string
): Workspace => {
  const newWs = JSON.parse(JSON.stringify(prevWs)) as Workspace;

  if (payload.eventType === 'INSERT' && payload.new) {
    const newMessage = transformSupabaseMessage(payload.new);
    const conversationId = newMessage.channelId;
    const parentMessageId = newMessage.parent_message_id;
    let messageAlreadyExists = false;

    let targetConversationForInsert: Channel | DirectMessage | undefined =
      newWs.sections.flatMap(s => s.channels).find(c => c.id === conversationId) ||
      newWs.directMessages.find(dm => dm.id === conversationId);

    if (!targetConversationForInsert) {
      return prevWs; // Message doesn't belong to a known conversation
    }

    if (parentMessageId) { // It's a reply
      const parentMessageInContext = targetConversationForInsert.messages.find(m => m.id === parentMessageId);
      if (parentMessageInContext) {
        parentMessageInContext.reply_count = (parentMessageInContext.reply_count || 0) + 1;
      }

      if (targetConversationForInsert.threads && targetConversationForInsert.threads[parentMessageId]) {
        const threadReplies = targetConversationForInsert.threads[parentMessageId];
        if (threadReplies.messages.some(m => m.id === newMessage.id)) {
          messageAlreadyExists = true;
        } else {
          threadReplies.messages.push(newMessage);
          threadReplies.messages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
          threadReplies.newestCursor = { timestamp: newMessage.timestamp, id: newMessage.id };
        }
      }
    } else { // It's a top-level message
      if (targetConversationForInsert.messages.some(m => m.id === newMessage.id)) {
        messageAlreadyExists = true;
      } else {
        targetConversationForInsert.messages.push(newMessage);
        targetConversationForInsert.messages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      }
    }

    if (messageAlreadyExists) return prevWs;

    // Update unread count for non-active conversations
    if (newMessage.userId !== currentAuthUserId) {
      const isActiveConversation = (targetConversationForInsert.id === newWs.currentChannelId) || (targetConversationForInsert.id === newWs.currentDirectMessageId);
      if (!isActiveConversation) {
        targetConversationForInsert.unreadCount = (targetConversationForInsert.unreadCount || 0) + 1;
        targetConversationForInsert.isUnreadCountPlaceholder = false;
      }
    }
  } else if (payload.eventType === 'DELETE' && payload.old) {
    const deletedMessage = transformSupabaseMessage(payload.old);
    const conversationId = deletedMessage.channelId;
    const parentMessageId = deletedMessage.parent_message_id;

    let targetConversationForDelete: Channel | DirectMessage | undefined =
      newWs.sections.flatMap(s => s.channels).find(c => c.id === conversationId) ||
      newWs.directMessages.find(dm => dm.id === conversationId);

    if (targetConversationForDelete) {
      if (parentMessageId) { // It's a reply being deleted
        const parentMessageInContext = targetConversationForDelete.messages.find(m => m.id === parentMessageId);
        if (parentMessageInContext && parentMessageInContext.reply_count && parentMessageInContext.reply_count > 0) {
          parentMessageInContext.reply_count--;
        }
        if (targetConversationForDelete.threads && targetConversationForDelete.threads[parentMessageId]) {
          const threadReplies = targetConversationForDelete.threads[parentMessageId];
          threadReplies.messages = threadReplies.messages.filter(m => m.id !== deletedMessage.id);
          // Potentially update cursors if the deleted message was a cursor
          if (threadReplies.oldestCursor?.id === deletedMessage.id && threadReplies.messages.length > 0) {
            threadReplies.oldestCursor = { timestamp: threadReplies.messages[0].timestamp, id: threadReplies.messages[0].id };
          } else if (threadReplies.oldestCursor?.id === deletedMessage.id) {
            threadReplies.oldestCursor = undefined;
            threadReplies.hasMoreOlder = false;
          }
          if (threadReplies.newestCursor?.id === deletedMessage.id && threadReplies.messages.length > 0) {
            threadReplies.newestCursor = { timestamp: threadReplies.messages[threadReplies.messages.length -1].timestamp, id: threadReplies.messages[threadReplies.messages.length -1].id };
          } else if (threadReplies.newestCursor?.id === deletedMessage.id) {
            threadReplies.newestCursor = undefined;
            threadReplies.hasMoreNewer = false;
          }
        }
      } else { // It's a top-level message being deleted
        targetConversationForDelete.messages = targetConversationForDelete.messages.filter(m => m.id !== deletedMessage.id);
        // If this top-level message had a thread, remove the thread data
        if (targetConversationForDelete.threads && targetConversationForDelete.threads[deletedMessage.id]) {
          delete targetConversationForDelete.threads[deletedMessage.id];
        }
      }
    }
  }
  return newWs;
};


/**
 * Applies the state update after successfully adding a new channel.
 */
export const applyAddChannelUpdateToWorkspace = (
  prevWs: Workspace,
  newChannel: Channel,
  targetSectionId: string
): Workspace => {
  const newSections = prevWs.sections.map(s =>
    s.id === targetSectionId
      ? { ...s, channels: [...s.channels, newChannel].sort((a, b) => a.name.localeCompare(b.name)) }
      : s
  );
  return {
    ...prevWs,
    sections: newSections,
    currentSectionId: targetSectionId,
    currentChannelId: newChannel.id,
    currentDirectMessageId: null,
    activeThreadId: null,
  };
};

/**
 * Applies the state update after adding a new section.
 */
export const applyAddSectionUpdateToWorkspace = (
  prevWs: Workspace,
  newSection: Section
): Workspace => {
  return { ...prevWs, sections: [...prevWs.sections, newSection] };
};

/**
 * Applies the state update after adding a new direct message.
 */
export const applyAddDirectMessageUpdateToWorkspace = (
  prevWs: Workspace,
  newDm: DirectMessage
): Workspace => {
  return {
    ...prevWs,
    directMessages: [...prevWs.directMessages, newDm],
    currentDirectMessageId: newDm.id,
    currentChannelId: null,
    currentSectionId: null,
    activeThreadId: null,
  };
};

/**
 * Applies an optimistic message update to the workspace state.
 */
export const applyOptimisticMessageUpdate = (
  prevWs: Workspace,
  optimisticMessage: Message,
  targetChannelId: string | null | undefined,
  targetDMId: string | null | undefined,
  parentMessageId: string | null | undefined // Renamed from threadId
): Workspace => {
  const newWs = JSON.parse(JSON.stringify(prevWs)) as Workspace;

  if (parentMessageId) {
    let targetConversation: Channel | DirectMessage | undefined;
    if (targetChannelId) {
      targetConversation = newWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelId);
    } else if (targetDMId) {
      targetConversation = newWs.directMessages.find((d: DirectMessage) => d.id === targetDMId);
    }

    if (targetConversation) {
      const parentMsg = targetConversation.messages.find(m => m.id === parentMessageId);
      if (parentMsg) {
        parentMsg.reply_count = (parentMsg.reply_count || 0) + 1;
      }

      if (!targetConversation.threads) targetConversation.threads = {};
      if (!targetConversation.threads[parentMessageId]) {
        targetConversation.threads[parentMessageId] = { messages: [], isLoadingOlder: false, isLoadingNewer: false, hasMoreOlder: false, hasMoreNewer: false };
      }
      targetConversation.threads[parentMessageId].messages.push(optimisticMessage);
      targetConversation.threads[parentMessageId].messages.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      // Update newest cursor optimistically
      const lastMsg = targetConversation.threads[parentMessageId].messages[targetConversation.threads[parentMessageId].messages.length -1];
      targetConversation.threads[parentMessageId].newestCursor = {timestamp: lastMsg.timestamp, id: lastMsg.id};

    }
  } else if (targetChannelId) {
    const ch = newWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelId);
    if (ch) {
      ch.messages.push(optimisticMessage);
      ch.messages.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    }
  } else if (targetDMId) {
    const dm = newWs.directMessages.find((d: DirectMessage) => d.id === targetDMId);
    if (dm) {
      dm.messages.push(optimisticMessage);
      dm.messages.sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    }
  }
  return newWs;
};

/**
 * Reverts an optimistic message update from the workspace state.
 */
export const revertOptimisticMessageUpdate = (
  prevWs: Workspace,
  clientTempId: string,
  targetChannelId: string | null | undefined,
  targetDMId: string | null | undefined,
  parentMessageId: string | null | undefined // Renamed from threadId
): Workspace => {
  const revertedWs = JSON.parse(JSON.stringify(prevWs)) as Workspace;

  if (parentMessageId) {
    let targetConversation: Channel | DirectMessage | undefined;
    if (targetChannelId) {
      targetConversation = revertedWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelId);
    } else if (targetDMId) {
      targetConversation = revertedWs.directMessages.find((d: DirectMessage) => d.id === targetDMId);
    }

    if (targetConversation) {
      const parentMsg = targetConversation.messages.find(m => m.id === parentMessageId);
      if (parentMsg && parentMsg.reply_count && parentMsg.reply_count > 0) {
        parentMsg.reply_count--;
      }
      if (targetConversation.threads && targetConversation.threads[parentMessageId]) {
        targetConversation.threads[parentMessageId].messages = targetConversation.threads[parentMessageId].messages.filter((m: Message) => m.id !== clientTempId);
      }
    }
  } else if (targetChannelId) {
    const ch = revertedWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelId);
    if (ch) {
      ch.messages = ch.messages.filter((m: Message) => m.id !== clientTempId);
    }
  } else if (targetDMId) {
    const dm = revertedWs.directMessages.find((d: DirectMessage) => d.id === targetDMId);
    if (dm) {
      dm.messages = dm.messages.filter((m: Message) => m.id !== clientTempId);
    }
  }
  return revertedWs;
};

/**
 * Applies an update to a specific channel within the workspace state.
 */
export const applyUpdateChannelToWorkspace = (
  prevWs: Workspace,
  updatedChannel: Channel
): Workspace => {
  const newSections = prevWs.sections.map(s => ({
    ...s,
    channels: s.channels.map(ch =>
      ch.id === updatedChannel.id ? { ...ch, ...updatedChannel } : ch
    ),
  }));
  return { ...prevWs, sections: newSections };
};

/**
 * Applies the state update for setting the current section.
 */
export const applySetCurrentSectionToWorkspace = (
  prevWs: Workspace,
  sectionId: string | null
): Workspace => {
  return {
    ...prevWs,
    currentSectionId: sectionId,
    currentChannelId: null,
    currentDirectMessageId: null,
    activeThreadId: null,
  };
};

/**
 * Applies the state update for setting the current channel.
 */
export const applySetCurrentChannelToWorkspace = (
  prevWs: Workspace,
  channelId: string | null,
  sectionIdForChannel: string | null
): Workspace => {
  return {
    ...prevWs,
    currentSectionId: sectionIdForChannel,
    currentChannelId: channelId,
    currentDirectMessageId: null,
    activeThreadId: null,
  };
};

/**
 * Applies the state update for setting the current direct message.
 */
export const applySetCurrentDirectMessageToWorkspace = (
  prevWs: Workspace,
  directMessageId: string | null
): Workspace => {
  return {
    ...prevWs,
    currentDirectMessageId: directMessageId,
    currentChannelId: null,
    currentSectionId: null,
    activeThreadId: null,
  };
};

/**
 * Applies the state update for setting the active thread (parent_message_id).
 */
export const applySetActiveThreadToWorkspace = (
  prevWs: Workspace,
  parentMessageId: string | null,
  _activeContext: Channel | DirectMessage | null
): Workspace => {
  return { ...prevWs, activeThreadId: parentMessageId };
};

/**
 * Marks a conversation (channel or DM) as read by setting its unreadCount to 0.
 */
export const applyMarkConversationReadToWorkspace = (
  prevWs: Workspace,
  conversationId: string,
  type: 'channel' | 'dm'
): Workspace => {
  if (type === 'channel') {
    const newSections = prevWs.sections.map(s => ({
      ...s,
      channels: s.channels.map(ch =>
        ch.id === conversationId ? { ...ch, unreadCount: 0, isUnreadCountPlaceholder: false } : ch
      ),
    }));
    return { ...prevWs, sections: newSections };
  } else { 
    const newDirectMessages = prevWs.directMessages.map(dm =>
      dm.id === conversationId ? { ...dm, unreadCount: 0, isUnreadCountPlaceholder: false } : dm
    );
    return { ...prevWs, directMessages: newDirectMessages };
  }
};

/**
 * Updates the settings for a specific user within the workspace state.
 */
export const applyUpdateUserSettingToWorkspace = (
  prevWs: Workspace,
  userId: string,
  newSettings: UserSettings
): Workspace => {
  const newUsers = prevWs.users.map(u =>
    u.id === userId ? { ...u, settings: newSettings } : u
  );
  return { ...prevWs, users: newUsers };
};

/**
 * Updates the general settings for the workspace.
 */
export const applyUpdateWorkspaceSettingsToWorkspace = (
  prevWs: Workspace,
  newSettings: Partial<WorkspaceSettings>
): Workspace => {
  const updatedSettings = { ...prevWs.settings, ...newSettings };
  return { ...prevWs, settings: updatedSettings };
};

/**
 * Updates the status and status message for a specific user.
 */
export const applyUpdateUserStatusToWorkspace = (
  prevWs: Workspace,
  userId: string,
  status: User['status'],
  statusMessage?: string
): Workspace => {
  const newUsers = prevWs.users.map(u =>
    u.id === userId ? { ...u, status, statusMessage } : u
  );
  return { ...prevWs, users: newUsers };
};

/**
 * Sets the active topic for a specific channel.
 */
export const applyNavigateToChannelTopicToWorkspace = (
  prevWs: Workspace,
  channelId: string,
  topicId: string
): Workspace => {
  const newSections = prevWs.sections.map(s => ({
    ...s,
    channels: s.channels.map(c =>
      c.id === channelId ? { ...c, activeChannelTopicId: topicId } : c
    ),
  }));
  return { ...prevWs, sections: newSections };
};

/**
 * Clears the active topic for a specific channel.
 */
export const applyClearActiveChannelTopicToWorkspace = (
  prevWs: Workspace,
  channelId: string
): Workspace => {
  const newSections = prevWs.sections.map(s => ({
    ...s,
    channels: s.channels.map(c =>
      c.id === channelId ? { ...c, activeChannelTopicId: undefined } : c
    ),
  }));
  return { ...prevWs, sections: newSections };
};

/**
 * Applies a reaction update to a message within the workspace state.
 */
export const applyReactionUpdateToWorkspace = (
  prevWs: Workspace,
  messageId: string,
  emoji: string,
  currentAuthUserId: string
): Workspace => {
  const newWs = JSON.parse(JSON.stringify(prevWs)) as Workspace;

  const processMsgReactions = (msg: Message) => {
    if (msg.id === messageId) {
      msg.reactions_summary = msg.reactions_summary || [];
      let reactionSummary = msg.reactions_summary.find(rs => rs.emoji === emoji);

      if (reactionSummary) {
        const userReactedIndex = reactionSummary.user_ids_array.indexOf(currentAuthUserId);
        if (userReactedIndex > -1) {
          reactionSummary.count--;
          reactionSummary.user_ids_array.splice(userReactedIndex, 1);
          if (reactionSummary.count === 0) {
            msg.reactions_summary = msg.reactions_summary.filter(rs => rs.emoji !== emoji);
          }
        } else {
          reactionSummary.count++;
          reactionSummary.user_ids_array.push(currentAuthUserId);
        }
      } else {
        msg.reactions_summary.push({
          emoji: emoji,
          count: 1,
          user_ids_array: [currentAuthUserId],
        });
      }
    }
  };

  newWs.sections.forEach((s: Section) =>
    s.channels.forEach((c: Channel) => {
      c.messages.forEach(processMsgReactions); // Top-level messages
      Object.values(c.threads || {}).forEach(thData => (thData as ThreadRepliesData).messages.forEach(processMsgReactions)); // Messages in loaded threads
    })
  );

  newWs.directMessages.forEach((dm: DirectMessage) => {
    dm.messages.forEach(processMsgReactions); // Top-level messages
    Object.values(dm.threads || {}).forEach(thData => (thData as ThreadRepliesData).messages.forEach(processMsgReactions)); // Messages in loaded threads
  });

  return newWs;
};
